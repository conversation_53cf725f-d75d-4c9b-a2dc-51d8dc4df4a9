.ai-prompt-optimizer-button {
  /* background-color: #10a37f; */
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px;
  margin: 8px 0;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  width: 32px;
  height: 32px;
  position: relative;
  background-color: transparent;
}

/* Critical thinking assistant button */
.critical-thinking-assistant-button {
  width: 28px;
  height: 28px;
  margin: 0 4px;
  background-color: transparent;
  position: relative;
  z-index: 1;
  padding: 6px;
  align-self: center;
}

/* 图标样式 */
.optimizer-icon {
  width: -webkit-fill-available;
  height: -webkit-fill-available;
  display: block;
}

/* 加载状态 */
.ai-prompt-optimizer-button.loading {
  position: relative;
  overflow: hidden;
  pointer-events: none;
}

.ai-prompt-optimizer-button.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  z-index: 2;
}

.ai-prompt-optimizer-button.loading::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  z-index: 3;
  animation: spin 1s linear infinite;
}

/* Special loading state for critical thinking assistant button */
.critical-thinking-assistant-button.loading::after {
  background-color: rgba(0, 0, 0, 0.5);
}

.critical-thinking-assistant-button.loading::before {
  width: 12px;
  height: 12px;
  border-width: 2px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ai-prompt-optimizer-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.critical-thinking-assistant-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.ai-prompt-optimizer-button:disabled,
.ai-prompt-optimizer-button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
}

.ai-prompt-optimizer-button:disabled img,
.ai-prompt-optimizer-button.disabled img {
  opacity: 0.5;
}

/* Custom tooltip styles */
.ai-prompt-optimizer-button .tooltip-text {
  position: absolute;
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 1000;
  pointer-events: none;
  margin-left: 8px;
}

.ai-prompt-optimizer-button .tooltip-arrow {
  position: absolute;
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-width: 5px;
  border-style: solid;
  border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s, visibility 0.3s;
  pointer-events: none;
  margin-left: -2px;
}

/* Related questions and topics button tooltip styles */
.related-questions-topics-button .tooltip-text {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-left: 0;
  margin-top: 8px;
  z-index: 10000;
}

.ai-prompt-optimizer-button:hover .tooltip-text,
.ai-prompt-optimizer-button:hover .tooltip-arrow {
  visibility: visible;
  opacity: 1;
}

/* Ensure related questions tooltip is visible on hover without arrow */
.related-questions-topics-button:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Hide tooltip when button is in loading or disabled state */
.ai-prompt-optimizer-button.loading .tooltip-text,
.ai-prompt-optimizer-button.loading .tooltip-arrow,
.ai-prompt-optimizer-button.loading:hover .tooltip-text,
.ai-prompt-optimizer-button.loading:hover .tooltip-arrow,
.ai-prompt-optimizer-button.disabled .tooltip-text,
.ai-prompt-optimizer-button.disabled .tooltip-arrow,
.ai-prompt-optimizer-button.disabled:hover .tooltip-text,
.ai-prompt-optimizer-button.disabled:hover .tooltip-arrow,
.ai-prompt-optimizer-button:disabled .tooltip-text,
.ai-prompt-optimizer-button:disabled .tooltip-arrow,
.ai-prompt-optimizer-button:disabled:hover .tooltip-text,
.ai-prompt-optimizer-button:disabled:hover .tooltip-arrow,
.related-questions-topics-button.loading .tooltip-text,
.related-questions-topics-button.loading:hover .tooltip-text {
  visibility: hidden;
  opacity: 0;
}

/* Dropdown menu styles */
.ai-prompt-optimizer-dropdown {
  position: absolute;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  min-width: 160px;
  z-index: 10000;
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Dropdown positioning classes */
.ai-prompt-optimizer-dropdown.dropdown-above {
  margin-bottom: 8px;
  transform-origin: bottom center;
}

.ai-prompt-optimizer-dropdown.dropdown-below {
  margin-top: 8px;
  transform-origin: top center;
}

.ai-prompt-optimizer-dropdown-item {
  padding: 10px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.ai-prompt-optimizer-dropdown-item:hover {
  background-color: #f5f5f5;
}

/* Form field styles */
.ai-prompt-optimizer-form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.ai-prompt-optimizer-form-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  box-sizing: border-box;
  min-width: 0; /* 防止内容溢出 */
}

.ai-prompt-optimizer-form-label {
  font-weight: 500;
  font-size: 14px;
  color: #555;
  word-wrap: break-word;
  white-space: normal;
  line-height: 1.4;
}

.ai-prompt-optimizer-form-description {
  font-size: 12px;
  color: #777;
  margin-bottom: 4px;
  word-wrap: break-word;
  white-space: normal;
  line-height: 1.4;
}

.ai-prompt-optimizer-form-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 100%;
  background-color: white;
  width: -webkit-fill-available;
}

.ai-prompt-optimizer-form-input:focus {
  outline: none;
  border-color: #10a37f;
  box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

.ai-prompt-optimizer-form-input.textarea {
  min-height: 80px;
  resize: vertical;
  width: -webkit-fill-available;
}

.ai-prompt-optimizer-form-input.select {
  min-height: 38px;
  height: auto;
  cursor: pointer;
  padding-right: 30px; /* 为下拉箭头留出空间 */
  text-overflow: ellipsis;
  white-space: normal; /* 允许文本换行 */
  word-wrap: break-word;
}

/* Select wrapper and Other input styles */
.ai-prompt-optimizer-select-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  width: 100%;
  flex-wrap: wrap; /* 在空间不足时允许换行 */
  box-sizing: border-box;
  min-width: 0; /* 防止内容溢出 */
}

.ai-prompt-optimizer-select-wrapper .ai-prompt-optimizer-form-input.select {
  flex: 1;
  min-width: 120px; /* 设置最小宽度 */
  max-width: 100%; /* 确保不超出容器 */
  box-sizing: border-box;
}

.ai-prompt-optimizer-other-input-container {
  flex: 2;
  min-width: 150px; /* 设置最小宽度 */
  max-width: 100%; /* 确保不超出容器 */
  animation: fadeIn 0.2s ease-in-out;
  box-sizing: border-box;
}

.ai-prompt-optimizer-form-input.other-input {
  border-color: #10a37f;
  background-color: #f0f9f6;
  width: 100%;
  box-sizing: border-box;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(-8px); }
  to { opacity: 1; transform: translateX(0); }
}

.ai-prompt-optimizer-popup-reasoning {
  font-size: 13px;
  line-height: 1.4;
  color: #666;
  background-color: #fff3e0;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #ff9800;
  margin-bottom: 8px;
}

/* Platform-specific styles */

/* ChatGPT */
body.chat-gpt .ai-prompt-optimizer-button {
  margin-top: 8px;
  width: 100%;
}

/* ChatGPT toolbar button */
.absolute.end-3.bottom-0 .ai-prompt-optimizer-button {
  margin: 0 0 0 4px;
  /* Most styles are applied inline to match ChatGPT's UI */
}

/* ChatGPT related questions button */
.chatgpt-related-questions-button {
  margin: 0 4px;
}

/* Ensure ChatGPT buttons are visible */
[data-testid="copy-turn-action-button"] + .ai-prompt-optimizer-button,
[data-testid="copy-turn-action-button"] ~ .ai-prompt-optimizer-button {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Gemini */
body.gemini .ai-prompt-optimizer-button {
  margin-top: 8px;
  width: 100%;
}

/* Claude */
body.claude .ai-prompt-optimizer-button {
  margin-top: 8px;
  width: 100%;
}

/* DeepSeek */
body.deepseek .ai-prompt-optimizer-button {
  margin-top: 8px;
  width: 100%;
}

/* Popup styles */
.ai-prompt-optimizer-popup,
.ai-critical-thinking-assistant-popup {
  position: fixed;
  z-index: 10000;
  width: 720px;
  max-width: 90vw;
  max-height: 90vh;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  border-radius: 12px;
  background-color: #fff;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.95);
  transition: opacity 0.3s, transform 0.3s;
  top: 50%;
  left: 50%;
  overflow: hidden;
  box-sizing: border-box;
}

/* Error popup styles */
.ai-error-popup {
  width: 480px;
}

/* Overlay styles */
.ai-prompt-optimizer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s;
}

.ai-prompt-optimizer-popup-content {
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.ai-prompt-optimizer-popup-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  font-size: 18px;
  background-color: #10a37f;
  color: white;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Error header style */
.ai-prompt-optimizer-popup-header.error-header {
  background-color: #e74c3c;
}

.ai-prompt-optimizer-popup-body {
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  box-sizing: border-box;
}

.ai-prompt-optimizer-popup-label {
  font-weight: 600;
  font-size: 14px;
  color: #555;
  margin-bottom: 4px;
  word-wrap: break-word;
  white-space: normal;
  line-height: 1.4;
}

.ai-prompt-optimizer-popup-label.prompt-label {
  color: #10a37f;
  font-size: 16px;
  margin-top: 8px;
}

.ai-prompt-optimizer-popup-requirements {
  font-size: 13px;
  line-height: 1.4;
  color: #666;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.ai-prompt-optimizer-popup-prompt-container {
  background-color: #e6f7f1;
  border-left: 3px solid #10a37f;
  padding: 12px;
  border-radius: 4px;
}

.ai-prompt-optimizer-popup-text {
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

/* Markdown styles */
.ai-prompt-optimizer-popup-text code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 13px;
}

.ai-prompt-optimizer-popup-text pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 8px 0;
}

.ai-prompt-optimizer-popup-text pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  display: block;
}

.ai-prompt-optimizer-popup-text h1,
.ai-prompt-optimizer-popup-text h2,
.ai-prompt-optimizer-popup-text h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
}

.ai-prompt-optimizer-popup-text h1 {
  font-size: 18px;
}

.ai-prompt-optimizer-popup-text h2 {
  font-size: 16px;
}

.ai-prompt-optimizer-popup-text h3 {
  font-size: 14px;
}

.ai-prompt-optimizer-popup-text ul,
.ai-prompt-optimizer-popup-text ol {
  padding-left: 24px;
  margin: 8px 0;
}

.ai-prompt-optimizer-popup-text li {
  margin-bottom: 4px;
}

.ai-prompt-optimizer-popup-text p {
  margin: 8px 0;
}

.ai-prompt-optimizer-popup-text br {
  display: block;
  content: "";
  margin-top: 8px;
}

/* Critical Analysis content styles */
.critical-analysis-content {
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
}

/* Scrollbar styles for critical analysis content */
.critical-analysis-content::-webkit-scrollbar {
  width: 8px;
}

.critical-analysis-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.critical-analysis-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.critical-analysis-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Enhanced markdown styles for critical analysis */
.critical-analysis-content h1,
.critical-analysis-content h2,
.critical-analysis-content h3,
.critical-analysis-content h4,
.critical-analysis-content h5,
.critical-analysis-content h6 {
  margin-top: 24px;
  margin-bottom: 12px;
  font-weight: 600;
  line-height: 1.3;
  color: #2c3e50;
}

.critical-analysis-content h1 {
  font-size: 24px;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.critical-analysis-content h2 {
  font-size: 20px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 6px;
}

.critical-analysis-content h3 {
  font-size: 18px;
}

.critical-analysis-content h4 {
  font-size: 16px;
}

.critical-analysis-content h5,
.critical-analysis-content h6 {
  font-size: 14px;
}

.critical-analysis-content p {
  margin: 12px 0;
  color: #333;
}

.critical-analysis-content ul,
.critical-analysis-content ol {
  margin: 12px 0;
  padding-left: 24px;
}

.critical-analysis-content li {
  margin-bottom: 6px;
}

.critical-analysis-content blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #10a37f;
  background-color: #f0f9f6;
  font-style: italic;
  color: #555;
}

.critical-analysis-content code {
  background-color: #f4f4f4;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #d73a49;
}

.critical-analysis-content pre {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 16px 0;
}

.critical-analysis-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  color: #24292e;
  font-size: 12px;
}

.critical-analysis-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.critical-analysis-content th,
.critical-analysis-content td {
  border: 1px solid #e0e0e0;
  padding: 8px 12px;
  text-align: left;
}

.critical-analysis-content th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.critical-analysis-content hr {
  border: none;
  border-top: 2px solid #e0e0e0;
  margin: 24px 0;
}

.critical-analysis-content strong {
  font-weight: 600;
  color: #2c3e50;
}

.critical-analysis-content em {
  font-style: italic;
  color: #555;
}

/* Related questions and topics list styles */
.ai-prompt-optimizer-popup-list {
  list-style: none;
  padding: 0;
  margin: 8px 0 16px;
}

.ai-prompt-optimizer-popup-list-item {
  padding: 8px 12px;
  margin-bottom: 4px;
  background-color: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  line-height: 1.4;
}

.ai-prompt-optimizer-popup-list-item:hover {
  background-color: #e0f2ea;
}

.ai-prompt-optimizer-popup-list-item.question-item::before {
  content: "Q: ";
  font-weight: 600;
  color: #10a37f;
}

.ai-prompt-optimizer-popup-list-item.topic-item::before {
  content: "# ";
  font-weight: 600;
  color: #10a37f;
}

/* Error container styles */
.ai-prompt-optimizer-popup-error-container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background-color: #fef2f2;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #e74c3c;
}

.ai-prompt-optimizer-popup-error-icon {
  width: 24px;
  height: 24px;
  min-width: 24px;
  border-radius: 50%;
  background-color: #e74c3c;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-prompt-optimizer-popup-error-icon::before,
.ai-prompt-optimizer-popup-error-icon::after {
  content: '';
  position: absolute;
  width: 2px;
  height: 12px;
  background-color: white;
  border-radius: 1px;
}

.ai-prompt-optimizer-popup-error-icon::before {
  transform: rotate(45deg);
}

.ai-prompt-optimizer-popup-error-icon::after {
  transform: rotate(-45deg);
}

.ai-prompt-optimizer-popup-text.error-text {
  color: #b91c1c;
  font-weight: 500;
}

.ai-prompt-optimizer-popup-footer {
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background-color: #f9f9f9;
}

.ai-prompt-optimizer-popup-button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ai-prompt-optimizer-popup-button.confirm {
  background-color: #10a37f;
  color: white;
}

.ai-prompt-optimizer-popup-button.confirm:hover {
  background-color: #0d8c6d;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.ai-prompt-optimizer-popup-button.cancel {
  background-color: #f5f5f5;
  color: #333;
}

.ai-prompt-optimizer-popup-button.cancel:hover {
  background-color: #e5e5e5;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ai-prompt-optimizer-popup,
  .ai-related-questions-topics-popup {
    background-color: #2d2d2d;
    color: #f5f5f5;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  }

  .ai-prompt-optimizer-popup-header {
    border-bottom-color: #444;
    background-color: #0d8c6d;
  }

  .ai-prompt-optimizer-popup-footer {
    border-top-color: #444;
    background-color: #222;
  }

  .ai-prompt-optimizer-popup-label {
    color: #bbb;
  }

  .ai-prompt-optimizer-popup-label.prompt-label {
    color: #4fd1ab;
  }

  .ai-prompt-optimizer-popup-requirements {
    color: #ccc;
    background-color: #3a3a3a;
  }

  .ai-prompt-optimizer-popup-prompt-container {
    background-color: #1e3b33;
    border-left-color: #4fd1ab;
  }

  .ai-prompt-optimizer-popup-text {
    color: #f0f0f0;
  }

  /* Dark mode markdown styles */
  .ai-prompt-optimizer-popup-text code {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* Dark mode tooltip styles */
  .ai-prompt-optimizer-button .tooltip-text {
    background-color: rgba(45, 45, 45, 0.9);
    color: #f5f5f5;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  }

  .ai-prompt-optimizer-button .tooltip-arrow {
    border-color: transparent rgba(45, 45, 45, 0.9) transparent transparent;
  }

  /* Dark mode related questions tooltip styles */
  .related-questions-topics-button .tooltip-text {
    background-color: rgba(45, 45, 45, 0.9);
    color: #f5f5f5;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  }

  /* Dark mode hover styles for buttons */
  .ai-prompt-optimizer-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .related-questions-topics-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .ai-prompt-optimizer-popup-text pre {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .ai-prompt-optimizer-popup-button.cancel {
    background-color: #444;
    color: #f5f5f5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .ai-prompt-optimizer-popup-button.cancel:hover {
    background-color: #555;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  .ai-prompt-optimizer-popup-button.confirm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .ai-prompt-optimizer-popup-button.confirm:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  /* Dark mode critical analysis styles */
  .critical-analysis-content {
    background-color: #2d2d2d;
    border-color: #444;
    color: #f0f0f0;
  }

  .critical-analysis-content::-webkit-scrollbar-track {
    background: #3a3a3a;
  }

  .critical-analysis-content::-webkit-scrollbar-thumb {
    background: #555;
  }

  .critical-analysis-content::-webkit-scrollbar-thumb:hover {
    background: #666;
  }

  .critical-analysis-content h1,
  .critical-analysis-content h2,
  .critical-analysis-content h3,
  .critical-analysis-content h4,
  .critical-analysis-content h5,
  .critical-analysis-content h6 {
    color: #f0f0f0;
  }

  .critical-analysis-content h1 {
    border-bottom-color: #444;
  }

  .critical-analysis-content h2 {
    border-bottom-color: #444;
  }

  .critical-analysis-content p {
    color: #e0e0e0;
  }

  .critical-analysis-content blockquote {
    border-left-color: #4fd1ab;
    background-color: #1e3b33;
    color: #ccc;
  }

  .critical-analysis-content code {
    background-color: #3a3a3a;
    color: #ff6b6b;
  }

  .critical-analysis-content pre {
    background-color: #1e1e1e;
    border-color: #444;
  }

  .critical-analysis-content pre code {
    color: #f0f0f0;
  }

  .critical-analysis-content th,
  .critical-analysis-content td {
    border-color: #444;
  }

  .critical-analysis-content th {
    background-color: #3a3a3a;
  }

  .critical-analysis-content hr {
    border-top-color: #444;
  }

  .critical-analysis-content strong {
    color: #f0f0f0;
  }

  .critical-analysis-content em {
    color: #ccc;
  }

  /* Dark mode related questions and topics list styles */
  .ai-prompt-optimizer-popup-list-item {
    background-color: #3a3a3a;
    color: #f0f0f0;
  }

  .ai-prompt-optimizer-popup-list-item:hover {
    background-color: #1e3b33;
  }

  .ai-prompt-optimizer-popup-list-item.question-item::before,
  .ai-prompt-optimizer-popup-list-item.topic-item::before {
    color: #4fd1ab;
  }

  /* Dark mode error styles */
  .ai-prompt-optimizer-popup-header.error-header {
    background-color: #c0392b;
  }

  .ai-prompt-optimizer-popup-error-container {
    background-color: #2c1a1a;
    border-left-color: #c0392b;
  }

  .ai-prompt-optimizer-popup-error-icon {
    background-color: #c0392b;
  }

  .ai-prompt-optimizer-popup-text.error-text {
    color: #e57373;
  }

  /* Dark mode overlay */
  .ai-prompt-optimizer-overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }

  /* Dark mode dropdown menu */
  .ai-prompt-optimizer-dropdown {
    background-color: #2d2d2d;
    border-color: #444;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .ai-prompt-optimizer-dropdown-item {
    color: #f0f0f0;
  }

  .ai-prompt-optimizer-dropdown-item:hover {
    background-color: #3a3a3a;
  }

  /* Dark mode form fields */
  .ai-prompt-optimizer-form-label {
    color: #bbb;
  }

  .ai-prompt-optimizer-form-description {
    color: #999;
  }

  .ai-prompt-optimizer-form-input {
    background-color: #333;
    border-color: #555;
    color: #f0f0f0;
  }

  .ai-prompt-optimizer-form-input:focus {
    border-color: #4fd1ab;
    box-shadow: 0 0 0 2px rgba(79, 209, 171, 0.2);
  }

  /* Dark mode Other input styles */
  .ai-prompt-optimizer-form-input.other-input {
    border-color: #4fd1ab;
    background-color: #1e3b33;
  }

  .ai-prompt-optimizer-popup-reasoning {
    background-color: #332b1a;
    border-left-color: #ff9800;
    color: #e0e0e0;
  }
}
